<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>chatBIS</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <!-- Markdown rendering library -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <!-- Syntax highlighting for code blocks -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/github.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js"></script>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <div class="header-content">
                <div class="header-text">
                    <h1><i class="fas fa-robot"></i> chatBIS</h1>
                    <p>Ask me anything about openBIS!</p>
                </div>
                <button id="clear-chat-btn" class="clear-btn" title="Clear conversation history">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
        <div class="chat-messages" id="chat-messages">
            <div class="message assistant">
                <div class="message-content">
                    <div class="markdown-content">
                        <p>Hello! I'm chatBIS, your personal openBIS assistant! How can I help you today?</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="chat-input">
            <form id="chat-form">
                <input type="text" id="user-input" placeholder="Type your message here..." autocomplete="off">
                <button type="submit"><i class="fas fa-paper-plane"></i></button>
            </form>
        </div>
        <div class="chat-footer">
            <p>Powered by BAM</p>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/script.js') }}"></script>
</body>
</html>
