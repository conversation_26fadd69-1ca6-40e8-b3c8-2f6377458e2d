<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>openBIS Chatbot Architecture</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .title {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 40px;
            font-size: 2.5em;
            font-weight: bold;
        }
        
        .subtitle {
            text-align: center;
            color: #7f8c8d;
            margin-bottom: 50px;
            font-size: 1.2em;
        }
        
        .architecture-flow {
            display: flex;
            flex-direction: column;
            gap: 30px;
            margin-bottom: 50px;
        }
        
        .layer {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 20px;
            position: relative;
        }
        
        .component {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            min-width: 200px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .component:hover {
            transform: translateY(-5px);
        }
        
        .component.user { background: linear-gradient(135deg, #e74c3c, #c0392b); }
        .component.interface { background: linear-gradient(135deg, #f39c12, #e67e22); }
        .component.engine { background: linear-gradient(135deg, #27ae60, #229954); }
        .component.data { background: linear-gradient(135deg, #9b59b6, #8e44ad); }
        .component.ai { background: linear-gradient(135deg, #1abc9c, #16a085); }
        
        .component h3 {
            margin: 0 0 10px 0;
            font-size: 1.3em;
        }
        
        .component p {
            margin: 0;
            font-size: 0.9em;
            opacity: 0.9;
        }
        
        .arrow {
            font-size: 2em;
            color: #34495e;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 0.6; }
            50% { opacity: 1; }
        }
        
        .data-flow {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 30px;
            margin: 30px 0;
        }
        
        .flow-title {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
            font-size: 1.8em;
            font-weight: bold;
        }
        
        .flow-steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }
        
        .step {
            background: white;
            border-left: 4px solid #3498db;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .step-number {
            background: #3498db;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-bottom: 15px;
        }
        
        .step h4 {
            margin: 0 0 10px 0;
            color: #2c3e50;
        }
        
        .step p {
            margin: 0;
            color: #7f8c8d;
            font-size: 0.9em;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 40px;
        }
        
        .feature {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 25px;
            border-radius: 12px;
            text-align: center;
        }
        
        .feature h3 {
            margin: 0 0 15px 0;
            font-size: 1.4em;
        }
        
        .feature ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .feature li {
            padding: 5px 0;
            font-size: 0.9em;
        }
        
        .feature li:before {
            content: "✓ ";
            color: #2ecc71;
            font-weight: bold;
        }
        
        .metrics {
            background: #2c3e50;
            color: white;
            border-radius: 12px;
            padding: 30px;
            margin-top: 30px;
            text-align: center;
        }
        
        .metrics h3 {
            margin: 0 0 20px 0;
            font-size: 1.8em;
        }
        
        .metric-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        
        .metric {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 8px;
        }
        
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            color: #3498db;
        }
        
        .metric-label {
            font-size: 0.9em;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">openBIS Chatbot Architecture</h1>
        <p class="subtitle">AI-Powered Documentation Assistant with Memory & RAG Technology</p>
        
        <div class="architecture-flow">
            <!-- User Layer -->
            <div class="layer">
                <div class="component user">
                    <h3>👥 Users</h3>
                    <p>Researchers, Lab Staff, Students</p>
                </div>
            </div>
            
            <div class="arrow">⬇️</div>
            
            <!-- Interface Layer -->
            <div class="layer">
                <div class="component interface">
                    <h3>🌐 Web Interface</h3>
                    <p>Browser-based chat UI<br>Session persistence</p>
                </div>
                <div class="component interface">
                    <h3>💻 CLI Interface</h3>
                    <p>Terminal-based chat<br>Developer-friendly</p>
                </div>
            </div>
            
            <div class="arrow">⬇️</div>
            
            <!-- Core Engine Layer -->
            <div class="layer">
                <div class="component engine">
                    <h3>🧠 Conversation Engine</h3>
                    <p>LangGraph Memory System<br>Context Management<br>Session Isolation</p>
                </div>
            </div>
            
            <div class="arrow">⬇️</div>
            
            <!-- Processing Layer -->
            <div class="layer">
                <div class="component ai">
                    <h3>🔍 RAG System</h3>
                    <p>Retrieval-Augmented Generation<br>195 Knowledge Chunks<br>Semantic Search</p>
                </div>
                <div class="component ai">
                    <h3>🤖 Local LLM</h3>
                    <p>Ollama (qwen3)<br>Private Processing<br>No External APIs</p>
                </div>
            </div>
            
            <div class="arrow">⬇️</div>
            
            <!-- Data Layer -->
            <div class="layer">
                <div class="component data">
                    <h3>📚 Knowledge Base</h3>
                    <p>openBIS Documentation<br>Embeddings & Chunks</p>
                </div>
                <div class="component data">
                    <h3>💾 Memory Store</h3>
                    <p>SQLite Database<br>Conversation History</p>
                </div>
            </div>
        </div>
        
        <div class="data-flow">
            <h2 class="flow-title">🔄 How a Conversation Works</h2>
            <div class="flow-steps">
                <div class="step">
                    <div class="step-number">1</div>
                    <h4>User Query</h4>
                    <p>"How do I create a collection in openBIS?"</p>
                </div>
                <div class="step">
                    <div class="step-number">2</div>
                    <h4>Session Check</h4>
                    <p>Load conversation history and context from memory</p>
                </div>
                <div class="step">
                    <div class="step-number">3</div>
                    <h4>Knowledge Retrieval</h4>
                    <p>Find relevant documentation chunks using semantic search</p>
                </div>
                <div class="step">
                    <div class="step-number">4</div>
                    <h4>Context Assembly</h4>
                    <p>Combine history + knowledge + current query</p>
                </div>
                <div class="step">
                    <div class="step-number">5</div>
                    <h4>AI Generation</h4>
                    <p>Local LLM generates contextually aware response</p>
                </div>
                <div class="step">
                    <div class="step-number">6</div>
                    <h4>Response Delivery</h4>
                    <p>Clean, user-friendly answer with memory update</p>
                </div>
            </div>
        </div>
        
        <div class="features">
            <div class="feature">
                <h3>🧠 Memory Features</h3>
                <ul>
                    <li>Remembers user names</li>
                    <li>Tracks conversation context</li>
                    <li>Recalls previous questions</li>
                    <li>Session isolation</li>
                    <li>Persistent across restarts</li>
                </ul>
            </div>
            <div class="feature">
                <h3>🔒 Security & Privacy</h3>
                <ul>
                    <li>100% local processing</li>
                    <li>No external API calls</li>
                    <li>GDPR compliant</li>
                    <li>Data stays on premises</li>
                    <li>Audit logging</li>
                </ul>
            </div>
            <div class="feature">
                <h3>⚡ Performance</h3>
                <ul>
                    <li>1-2 second responses</li>
                    <li>10-50 concurrent users</li>
                    <li>Scalable architecture</li>
                    <li>Efficient memory usage</li>
                    <li>Container ready</li>
                </ul>
            </div>
        </div>
        
        <div class="metrics">
            <h3>📊 Key Performance Metrics</h3>
            <div class="metric-grid">
                <div class="metric">
                    <div class="metric-value">1-2s</div>
                    <div class="metric-label">Response Time</div>
                </div>
                <div class="metric">
                    <div class="metric-value">195</div>
                    <div class="metric-label">Knowledge Chunks</div>
                </div>
                <div class="metric">
                    <div class="metric-value">20</div>
                    <div class="metric-label">Messages Memory</div>
                </div>
                <div class="metric">
                    <div class="metric-value">50+</div>
                    <div class="metric-label">Concurrent Users</div>
                </div>
                <div class="metric">
                    <div class="metric-value">100%</div>
                    <div class="metric-label">Local Processing</div>
                </div>
                <div class="metric">
                    <div class="metric-value">24/7</div>
                    <div class="metric-label">Availability</div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
