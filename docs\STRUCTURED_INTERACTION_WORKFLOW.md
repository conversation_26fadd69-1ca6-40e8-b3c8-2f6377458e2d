# Structured Interaction Workflow for chatBIS

## Overview

The Structured Interaction Workflow is a new, robust system for handling openBIS entity creation, retrieval, and modification operations in chatBIS. This workflow introduces a standardized JSON intermediate representation and a user-in-the-loop validation step, making openBIS interactions more reliable and safer.

## Key Design Principles

1. **Separation of Concerns**: Uses LLMs for natural language understanding and deterministic Python code for API interactions
2. **User-in-the-Loop**: Requires user confirmation before any destructive operations (CREATE, UPDATE, DELETE)
3. **Standardization**: All entity interactions flow through a single, well-defined JSON schema
4. **Modularity**: Components are modular, testable, and maintainable
5. **Transaction Safety**: Supports atomic operations with rollback capabilities

## Architecture Components

### 1. ActionRequest JSON Schema (`src/chatBIS/models/entity.py`)

The core data structure that represents openBIS operations:

```json
{
  "actions": [
    {
      "action": "CREATE",
      "entity": "PROJECT",
      "location": {
        "space": "/MY_SPACE"
      },
      "payload": {
        "code": "MY_PROJECT_001",
        "type": "DEFAULT",
        "properties": {
          "DESCRIPTION": "Created via chatBIS"
        }
      }
    }
  ]
}
```

**Key Features:**
- **Action Types**: CREATE, GET, UPDATE, LIST, DELETE
- **Entity Types**: SPACE, PROJECT, EXPERIMENT, OBJECT, DATASET
- **Location Hierarchy**: Proper openBIS containment structure
- **Payload Data**: Properties, relationships, and metadata
- **Validation**: Pydantic models ensure data integrity

### 2. EntityStructurer (`src/chatBIS/tools/entity_structurer.py`)

LLM-based component that converts natural language to ActionRequest JSON:

**Functions:**
- `structure_user_request()`: Converts natural language to structured JSON
- `format_confirmation_summary()`: Creates human-readable summaries for confirmation

**Example Usage:**
```python
structurer = EntityStructurer(model="qwen3")
request = structurer.structure_user_request("Create a project called TEST_PROJECT in space MY_LAB")
summary = structurer.format_confirmation_summary(request)
```

### 3. PybisAdapter (`src/chatBIS/tools/pybis_adapter.py`)

Deterministic translator between ActionRequest JSON and pybis function calls:

**Key Methods:**
- `execute_actions()`: Executes ActionRequest with transaction support
- `pybis_to_json_response()`: Converts pybis objects to standardized JSON

**Features:**
- Transaction support for atomic operations
- Automatic rollback on failures
- Comprehensive error handling
- Support for all openBIS entity types

### 4. ConversationEngine Integration

The workflow is integrated into the existing LangGraph-based ConversationEngine with new nodes:

**New Workflow Nodes:**
1. **structure_request**: Convert natural language to ActionRequest JSON
2. **present_for_confirmation**: Show user what will be executed
3. **execute_interaction**: Execute actions via PybisAdapter
4. **report_result**: Format and present results

**Routing Logic:**
- **Structured Interaction**: CREATE/UPDATE/DELETE operations with specific entities
- **RAG**: Documentation and "how-to" questions (highest priority)
- **Function Call**: Simple queries and connections
- **Traditional Flow**: Maintains backward compatibility

## Workflow Flow

```mermaid
graph TD
    A[User Query] --> B[Router Agent]
    B -->|"create a project"| C[Structure Request]
    B -->|"how to create"| D[RAG Agent]
    B -->|"list projects"| E[Function Calling Agent]
    
    C --> F[Decide on Confirmation]
    F -->|Destructive Actions| G[Present for Confirmation]
    F -->|Read-only Actions| H[Execute Interaction]
    G --> H
    H --> I[Report Result]
    I --> J[Format Response]
    
    D --> J
    E --> J
    J --> K[Update Conversation]
    K --> L[End]
```

## Usage Examples

### Creating a Project
```
User: "Create a new project called CELL_CULTURE_2024 in my space"

System: "## Planned Actions

I'm ready to perform the following operations:

1. **Create Project**: `CELL_CULTURE_2024` (type: `DEFAULT`) in Space `/YOUR_SPACE`

⚠️  **Warning**: This request includes operations that will modify your openBIS instance.

**Please confirm:**
- Type 'yes' to proceed with these actions
- Type 'no' to cancel
- Describe any changes you'd like me to make"

User: "yes"

System: "## Execution Results

✅ Successfully completed all 1 actions:

1. ✅ CREATE PROJECT: Created successfully (ID: /YOUR_SPACE/CELL_CULTURE_2024)"
```

### Complex Multi-Entity Creation
```
User: "Create experiment EXP_001 in project /MY_LAB/PROJECT_A and add a sample SAMPLE_001 of type CHEMICAL"

System: "## Planned Actions

I'm ready to perform the following operations:

1. **Create Experiment**: `EXP_001` (type: `COLLECTION_EXPERIMENT`) in Project `/MY_LAB/PROJECT_A`
2. **Create Sample/Object**: `SAMPLE_001` (type: `CHEMICAL`) in Experiment `/MY_LAB/PROJECT_A/EXP_001`

📦 All actions will be executed as a single transaction (all succeed or all fail).

**Please confirm:**..."
```

## Configuration and Setup

### Prerequisites
- Python 3.8+
- pybis package
- Pydantic v2.x
- LangChain and LangGraph
- Ollama (optional, fallback methods available)

### Environment Setup
```bash
# Activate the chatbis environment
conda activate chatbis

# Install additional dependencies if needed
pip install pydantic>=2.0
```

### Testing
Run the comprehensive test suite:
```bash
python test_structured_workflow.py
```

## Error Handling and Safety

### Transaction Safety
- All destructive operations are wrapped in pybis transactions
- Automatic rollback on any failure
- Atomic operations ensure consistency

### Validation Layers
1. **Pydantic Validation**: Schema and type checking
2. **Business Logic Validation**: openBIS hierarchy and constraints
3. **Runtime Validation**: Connection and permission checks

### User Confirmation
- Required for all CREATE, UPDATE, DELETE operations
- Clear summary of planned actions
- Option to modify or cancel operations

## Extending the Workflow

### Adding New Action Types
1. Update `ActionType` literal in `entity.py`
2. Add handling in `PybisAdapter.execute_actions()`
3. Update validation logic in `Action.validate_action_requirements()`

### Adding New Entity Types
1. Update `EntityType` literal in `entity.py`
2. Add method mappings in `PybisAdapter.__init__()`
3. Update entity type detection in `_get_entity_type_from_pybis_object()`

### Customizing Confirmation Flow
- Modify `present_for_confirmation` node in ConversationEngine
- Implement interactive confirmation handling
- Add custom validation rules

## Migration from Legacy Function Calling

The new workflow is designed to coexist with the existing function calling system:

- **Backward Compatibility**: Existing queries continue to work
- **Gradual Migration**: New patterns automatically use structured workflow
- **Fallback Support**: Falls back to function calling when structured workflow fails

## Performance Considerations

- **Caching**: Entity type mappings and method lookups are cached
- **Batch Operations**: Multiple actions executed in single transaction
- **Lazy Loading**: Components initialized only when needed
- **Memory Management**: Large result sets handled efficiently

## Security Considerations

- **User Confirmation**: Prevents accidental destructive operations
- **Transaction Isolation**: Operations are atomic and isolated
- **Input Validation**: Multiple layers of validation prevent injection
- **Permission Checking**: Respects openBIS user permissions

## Future Enhancements

1. **Interactive Confirmation**: Real-time user interaction in web interface
2. **Operation History**: Track and audit all structured operations
3. **Template System**: Predefined operation templates
4. **Bulk Operations**: Enhanced support for large-scale operations
5. **Workflow Scheduling**: Delayed and scheduled operations
