/* Chatbot Styles */
.chatbot-trigger {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: #4a6fa5;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    z-index: 1000;
    transition: all 0.3s ease;
    border: none;
    font-size: 24px;
}

.chatbot-trigger:hover {
    transform: scale(1.1);
    background-color: #3a5f95;
    box-shadow: 0 6px 16px rgba(0,0,0,0.2);
}

.chatbot-container {
    position: fixed;
    bottom: 90px;
    right: 20px;
    width: 380px;
    height: 550px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.12);
    display: none;
    z-index: 1000;
    overflow: hidden;
    flex-direction: column;
    border: 1px solid #e0e0e0;
}

/* Responsive design for mobile */
@media (max-width: 480px) {
    .chatbot-container {
        width: calc(100vw - 40px);
        height: calc(100vh - 140px);
        bottom: 90px;
        right: 20px;
        left: 20px;
    }

    .chatbot-trigger {
        bottom: 20px;
        right: 20px;
    }
}

.chatbot-header {
    background-color: #4a6fa5;
    color: white;
    padding: 16px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.chatbot-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.chatbot-close {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
    font-size: 16px;
}

.chatbot-close:hover {
    background-color: rgba(255,255,255,0.1);
}

.chatbot-messages {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background-color: #fafafa;
}

.chatbot-input {
    padding: 16px 20px;
    border-top: 1px solid #e0e0e0;
    display: flex;
    gap: 12px;
    background: white;
}

.chatbot-input input {
    flex: 1;
    padding: 12px 16px;
    border: 1px solid #ddd;
    border-radius: 24px;
    outline: none;
    font-size: 14px;
    transition: border-color 0.2s ease;
}

.chatbot-input input:focus {
    border-color: #4a6fa5;
}

.chatbot-input input:disabled {
    background-color: #f5f5f5;
    cursor: not-allowed;
}

.chatbot-input button {
    background-color: #4a6fa5;
    color: white;
    border: none;
    padding: 12px 16px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 44px;
    height: 44px;
}

.chatbot-input button:hover:not(:disabled) {
    background-color: #3a5f95;
    transform: scale(1.05);
}

.chatbot-input button:disabled {
    background-color: #ccc;
    cursor: not-allowed;
    transform: none;
}

.chatbot-message {
    margin-bottom: 16px;
    display: flex;
    flex-direction: column;
    animation: fadeIn 0.3s ease-in;
}

.chatbot-message.user {
    align-items: flex-end;
}

.chatbot-message.assistant {
    align-items: flex-start;
}

.message-content {
    max-width: 85%;
    padding: 12px 16px;
    border-radius: 18px;
    background-color: #f0f0f0;
    font-size: 14px;
    line-height: 1.4;
    word-wrap: break-word;
}

.chatbot-message.user .message-content {
    background-color: #4a6fa5;
    color: white;
    border-bottom-right-radius: 4px;
}

.chatbot-message.assistant .message-content {
    background-color: white;
    border: 1px solid #e0e0e0;
    border-bottom-left-radius: 4px;
}

/* Markdown styling within messages */
.message-content h1, .message-content h2, .message-content h3 {
    margin: 8px 0 4px 0;
    color: inherit;
}

.message-content h1 { font-size: 18px; }
.message-content h2 { font-size: 16px; }
.message-content h3 { font-size: 14px; }

.message-content code {
    background-color: rgba(0,0,0,0.1);
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
    font-size: 13px;
}

.message-content pre {
    background-color: rgba(0,0,0,0.1);
    padding: 8px;
    border-radius: 6px;
    overflow-x: auto;
    margin: 8px 0;
}

.message-content pre code {
    background: none;
    padding: 0;
}

.message-content a {
    color: #4a6fa5;
    text-decoration: none;
}

.message-content a:hover {
    text-decoration: underline;
}

.chatbot-message.user .message-content code,
.chatbot-message.user .message-content pre {
    background-color: rgba(255,255,255,0.2);
}

.chatbot-message.user .message-content a {
    color: #cce7ff;
}

/* Loading indicator */
.chatbot-loading {
    padding: 16px 20px;
    text-align: center;
    border-top: 1px solid #e0e0e0;
    background: white;
}

.loading-dots {
    display: inline-flex;
    gap: 4px;
}

.loading-dots span {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #4a6fa5;
    animation: loadingDots 1.4s infinite ease-in-out both;
}

.loading-dots span:nth-child(1) { animation-delay: -0.32s; }
.loading-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes loadingDots {
    0%, 80%, 100% {
        transform: scale(0);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Scrollbar styling */
.chatbot-messages::-webkit-scrollbar {
    width: 6px;
}

.chatbot-messages::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.chatbot-messages::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.chatbot-messages::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}