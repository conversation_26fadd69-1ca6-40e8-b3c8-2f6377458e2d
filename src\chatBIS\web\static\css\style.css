/* General Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    background-color: #f5f7fb;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 20px;
}

/* Chat Container */
.chat-container {
    width: 100%;
    max-width: 800px;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    height: 80vh;
    max-height: 700px;
    overflow: hidden;
}

/* Chat Header */
.chat-header {
    padding: 20px;
    background-color: #4a6fa5;
    color: white;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-text h1 {
    margin: 0;
    font-size: 1.5em;
}

.header-text p {
    margin: 5px 0 0 0;
    opacity: 0.9;
    font-size: 0.9em;
}

.clear-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    padding: 10px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 1em;
    transition: background-color 0.3s ease;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.clear-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

.clear-btn:active {
    background: rgba(255, 255, 255, 0.4);
}

.chat-header h1 {
    font-size: 1.5rem;
    margin-bottom: 5px;
    display: flex;
    align-items: center;
}

.chat-header h1 i {
    margin-right: 10px;
}

.chat-header p {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Chat Messages */
.chat-messages {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.message {
    display: flex;
    margin-bottom: 10px;
}

.message.user {
    justify-content: flex-end;
}

.message-content {
    padding: 12px 16px;
    border-radius: 18px;
    max-width: 70%;
    word-wrap: break-word;
}

.user .message-content {
    background-color: #4a6fa5;
    color: white;
    border-top-right-radius: 4px;
}

.assistant .message-content {
    background-color: #f0f2f5;
    color: #333;
    border-top-left-radius: 4px;
}

.message-content p {
    margin: 0;
    line-height: 1.4;
}

/* Markdown Content Styles */
.markdown-content {
    line-height: 1.6;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
    margin: 16px 0 8px 0;
    font-weight: 600;
    color: #2c3e50;
}

.markdown-content h1 { font-size: 1.5em; }
.markdown-content h2 { font-size: 1.3em; }
.markdown-content h3 { font-size: 1.2em; }
.markdown-content h4 { font-size: 1.1em; }
.markdown-content h5 { font-size: 1.05em; }
.markdown-content h6 { font-size: 1em; }

.markdown-content p {
    margin: 8px 0;
    line-height: 1.6;
}

.markdown-content ul,
.markdown-content ol {
    margin: 8px 0;
    padding-left: 20px;
}

.markdown-content li {
    margin: 4px 0;
    line-height: 1.5;
}

.markdown-content strong {
    font-weight: 600;
    color: #2c3e50;
}

.markdown-content em {
    font-style: italic;
}

.markdown-content code {
    background-color: #f1f3f4;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.9em;
    color: #d73a49;
}

.markdown-content pre {
    background-color: #f6f8fa;
    border: 1px solid #e1e4e8;
    border-radius: 6px;
    padding: 16px;
    margin: 12px 0;
    overflow-x: auto;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.85em;
    line-height: 1.45;
}

.markdown-content pre code {
    background: none;
    padding: 0;
    border-radius: 0;
    color: inherit;
    font-size: inherit;
}

.markdown-content blockquote {
    border-left: 4px solid #dfe2e5;
    padding-left: 16px;
    margin: 12px 0;
    color: #6a737d;
    font-style: italic;
}

.markdown-content table {
    border-collapse: collapse;
    margin: 12px 0;
    width: 100%;
}

.markdown-content th,
.markdown-content td {
    border: 1px solid #dfe2e5;
    padding: 8px 12px;
    text-align: left;
}

.markdown-content th {
    background-color: #f6f8fa;
    font-weight: 600;
}

.markdown-content hr {
    border: none;
    border-top: 1px solid #e1e4e8;
    margin: 16px 0;
}

.markdown-content a {
    color: #0366d6;
    text-decoration: none;
}

.markdown-content a:hover {
    text-decoration: underline;
}

/* Chat Input */
.chat-input {
    padding: 15px;
    border-top: 1px solid #e6e6e6;
}

.chat-input form {
    display: flex;
    gap: 10px;
}

.chat-input input {
    flex: 1;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 25px;
    outline: none;
    font-size: 1rem;
}

.chat-input input:focus {
    border-color: #4a6fa5;
}

.chat-input button {
    background-color: #4a6fa5;
    color: white;
    border: none;
    border-radius: 50%;
    width: 45px;
    height: 45px;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: background-color 0.2s;
}

.chat-input button:hover {
    background-color: #3a5a8f;
}

.chat-input button i {
    font-size: 1.2rem;
}

/* Chat Footer */
.chat-footer {
    padding: 10px;
    text-align: center;
    font-size: 0.8rem;
    color: #888;
    border-top: 1px solid #e6e6e6;
}

/* Loading Animation */
.loading {
    display: flex;
    padding: 12px 16px;
    background-color: #f0f2f5;
    border-radius: 18px;
    border-top-left-radius: 4px;
    max-width: 70px;
}

.loading span {
    width: 8px;
    height: 8px;
    margin: 0 2px;
    background-color: #999;
    border-radius: 50%;
    display: inline-block;
    animation: loading 1.4s infinite ease-in-out both;
}

.loading span:nth-child(1) {
    animation-delay: -0.32s;
}

.loading span:nth-child(2) {
    animation-delay: -0.16s;
}

@keyframes loading {
    0%, 80%, 100% {
        transform: scale(0);
    }
    40% {
        transform: scale(1.0);
    }
}

/* Responsive Design */
@media (max-width: 600px) {
    .chat-container {
        height: 90vh;
        max-height: none;
        border-radius: 0;
    }

    .chat-header {
        border-radius: 0;
    }

    .message-content {
        max-width: 85%;
    }
}
