// Chatbot configuration
const ChatbotConfig = {
    // Default configuration - update CHATBOT_API_URL to point to your openbis-chatbot-v1 backend
    apiUrl: 'https://chatbot.datastore.bam.de/api/chat', // e.g., 'http://localhost:5000/api/chat'

    // Fallback configuration for development
    defaultHost: window.location.protocol + '//' + window.location.hostname,
    defaultPort: '5000',

    // Get the full API endpoint URL
    getApiEndpoint: function() {
        // If apiUrl is already a full URL, use it directly
        if (this.apiUrl.startsWith('http://') || this.apiUrl.startsWith('https://')) {
            return this.apiUrl;
        }
        // Otherwise, construct from parts (for backward compatibility)
        return this.defaultHost + ':' + this.defaultPort + '/api/chat';
    },

    // Session configuration
    sessionStorageKey: 'openbis-chatbot-session-id',

    // UI configuration
    ui: {
        title: 'chatBIS',
        placeholder: 'Ask me anything about openBIS...',
        welcomeMessage: 'Hello! I\'m chat<PERSON><PERSON>, your openBIS assistant. How can I help you today?'
    }
};

// Export configuration
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ChatbotConfig;
} else {
    window.ChatbotConfig = ChatbotConfig;
}