<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>openBIS Chatbot - Markdown Rendering Demo</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f7fb;
        }
        
        .demo-container {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }
        
        .title {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .before, .after {
            border: 2px solid #e1e4e8;
            border-radius: 8px;
            padding: 20px;
        }
        
        .before {
            border-color: #dc3545;
            background-color: #fff5f5;
        }
        
        .after {
            border-color: #28a745;
            background-color: #f8fff8;
        }
        
        .section-title {
            font-weight: bold;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .before .section-title {
            color: #dc3545;
        }
        
        .after .section-title {
            color: #28a745;
        }
        
        .raw-text {
            font-family: monospace;
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-size: 0.9em;
            line-height: 1.4;
        }
        
        /* Copy the markdown styles from the main CSS */
        .markdown-content {
            line-height: 1.6;
        }
        
        .markdown-content h1,
        .markdown-content h2,
        .markdown-content h3,
        .markdown-content h4,
        .markdown-content h5,
        .markdown-content h6 {
            margin: 16px 0 8px 0;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .markdown-content h1 { font-size: 1.5em; }
        .markdown-content h2 { font-size: 1.3em; }
        .markdown-content h3 { font-size: 1.2em; }
        .markdown-content h4 { font-size: 1.1em; }
        .markdown-content h5 { font-size: 1.05em; }
        .markdown-content h6 { font-size: 1em; }
        
        .markdown-content p {
            margin: 8px 0;
            line-height: 1.6;
        }
        
        .markdown-content ul,
        .markdown-content ol {
            margin: 8px 0;
            padding-left: 20px;
        }
        
        .markdown-content li {
            margin: 4px 0;
            line-height: 1.5;
        }
        
        .markdown-content strong {
            font-weight: 600;
            color: #2c3e50;
        }
        
        .markdown-content em {
            font-style: italic;
        }
        
        .markdown-content code {
            background-color: #f1f3f4;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.9em;
            color: #d73a49;
        }
        
        .markdown-content pre {
            background-color: #f6f8fa;
            border: 1px solid #e1e4e8;
            border-radius: 6px;
            padding: 16px;
            margin: 12px 0;
            overflow-x: auto;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.85em;
            line-height: 1.45;
        }
        
        .markdown-content pre code {
            background: none;
            padding: 0;
            border-radius: 0;
            color: inherit;
            font-size: inherit;
        }
        
        .features {
            background: #e8f5e8;
            border-radius: 8px;
            padding: 20px;
            margin-top: 30px;
        }
        
        .features h3 {
            color: #28a745;
            margin-bottom: 15px;
        }
        
        .features ul {
            list-style-type: none;
            padding: 0;
        }
        
        .features li {
            padding: 5px 0;
        }
        
        .features li:before {
            content: "✅ ";
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1 class="title">openBIS Chatbot - Markdown Rendering Improvement</h1>
        
        <div class="comparison">
            <div class="before">
                <div class="section-title">❌ BEFORE: Raw Markdown Text</div>
                <div class="raw-text">**openBIS** is an open-source, web-based **research data management system** designed to help scientists organize, store, and share complex data from experiments, simulations, and observations. It's widely used in fields like biology, physics, and materials science to manage large datasets while maintaining **metadata**, **provenance**, and **collaboration**. ### Key Features: 1. **Metadata Management**: Tracks details about your data (e.g., "What was measured? When? By whom?"). 2. **Data Organization**: Stores files, images, tables, and raw data in structured folders. 3. **Collaboration**: Lets teams share data, annotate files, and link to publications or tools. 4. **Integration**: Connects to tools like **Jupyter Notebooks**, **Dropbox**, or **APIs** for seamless workflows. 5. **Search & Discovery**: Finds data across projects using keywords, tags, or metadata. ### Why Use openBIS? - Keeps your data **discoverable** and **reproducible**. - Helps meet **funding requirements** for data sharing. - Simplifies **team collaboration** and **data versioning**. Let me know if you'd like help setting it up or exploring its tools! 😊</div>
            </div>
            
            <div class="after">
                <div class="section-title">✅ AFTER: Properly Rendered Markdown</div>
                <div class="markdown-content">
                    <p><strong>openBIS</strong> is an open-source, web-based <strong>research data management system</strong> designed to help scientists organize, store, and share complex data from experiments, simulations, and observations. It's widely used in fields like biology, physics, and materials science to manage large datasets while maintaining <strong>metadata</strong>, <strong>provenance</strong>, and <strong>collaboration</strong>.</p>
                    
                    <h3>Key Features:</h3>
                    <ol>
                        <li><strong>Metadata Management</strong>: Tracks details about your data (e.g., "What was measured? When? By whom?").</li>
                        <li><strong>Data Organization</strong>: Stores files, images, tables, and raw data in structured folders.</li>
                        <li><strong>Collaboration</strong>: Lets teams share data, annotate files, and link to publications or tools.</li>
                        <li><strong>Integration</strong>: Connects to tools like <strong>Jupyter Notebooks</strong>, <strong>Dropbox</strong>, or <strong>APIs</strong> for seamless workflows.</li>
                        <li><strong>Search & Discovery</strong>: Finds data across projects using keywords, tags, or metadata.</li>
                    </ol>
                    
                    <h3>Why Use openBIS?</h3>
                    <ul>
                        <li>Keeps your data <strong>discoverable</strong> and <strong>reproducible</strong>.</li>
                        <li>Helps meet <strong>funding requirements</strong> for data sharing.</li>
                        <li>Simplifies <strong>team collaboration</strong> and <strong>data versioning</strong>.</li>
                    </ul>
                    
                    <p>Let me know if you'd like help setting it up or exploring its tools! 😊</p>
                </div>
            </div>
        </div>
        
        <div class="features">
            <h3>🚀 New Markdown Features Supported</h3>
            <ul>
                <li><strong>Bold text</strong> and <em>italic text</em> formatting</li>
                <li>Properly structured headings (H1-H6)</li>
                <li>Ordered and unordered lists with proper spacing</li>
                <li>Inline <code>code snippets</code> with syntax highlighting</li>
                <li>Code blocks with language-specific highlighting</li>
                <li>Tables with proper borders and styling</li>
                <li>Blockquotes for important information</li>
                <li>Horizontal rules for section separation</li>
                <li>Clickable links that open in new tabs</li>
                <li>Line breaks and paragraph spacing</li>
                <li>GitHub Flavored Markdown support</li>
                <li>Fallback to plain text if rendering fails</li>
            </ul>
        </div>
        
        <div class="demo-container">
            <h2>📋 Implementation Details</h2>
            <div class="markdown-content">
                <h3>Libraries Used:</h3>
                <ul>
                    <li><strong>Marked.js</strong>: Fast markdown parser and compiler</li>
                    <li><strong>Highlight.js</strong>: Syntax highlighting for code blocks</li>
                    <li><strong>Custom CSS</strong>: GitHub-style markdown rendering</li>
                </ul>
                
                <h3>Key Improvements:</h3>
                <ul>
                    <li>Assistant messages now render markdown properly</li>
                    <li>User messages remain as plain text for clarity</li>
                    <li>Error handling with fallback to plain text</li>
                    <li>External links open in new tabs for security</li>
                    <li>Responsive design that works on all devices</li>
                </ul>
                
                <h3>Technical Configuration:</h3>
                <pre><code>marked.setOptions({
    breaks: true,        // Convert line breaks to &lt;br&gt;
    gfm: true,          // GitHub Flavored Markdown
    headerIds: false,   // Don't add IDs to headers
    mangle: false,      // Don't escape autolinked email addresses
    sanitize: false,    // Don't sanitize HTML (we trust our content)
    smartLists: true,   // Use smarter list behavior
    smartypants: false  // Don't use smart quotes
});</code></pre>
            </div>
        </div>
    </div>
</body>
</html>
